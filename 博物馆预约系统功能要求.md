预约系统界面首页可用博物馆照片为背景，显示博物馆的开闭馆时间、地址、个人预约、团体预约、个人中心（包括个人预约查询、添加人员管理）。点开相应的词条后有相应的参观须知（参观须知内容另附）。  
个人预约  
1.预约通道登录后需要实名认证，认证信息在预约时直接显示，不需要重复输入。需勾选或者阅读“中国（哈尔滨）森林博物馆预约的隐私协议”（内容另附）。  
2.身份证填写可以自动识别位数和年龄进行提示，身份证信息填写有误提示，14周岁以下不可单独预约。  
3.在预约界面上可以选择添加同行人信息，一人最多可同时预约5人，同样识别身份证入馆。携带儿童也需填写身份证信息。预约界面填写信息刷新后可保留，无需重新填写。

![](https://cdn.nlark.com/yuque/0/2025/png/38454556/1753431162151-6a8416ec-ef04-444b-8969-943d0b8d8b40.png)

（四川大学博物馆）

4.    每周二早9:00放本周周四周五周六的号。  
预约日期和时间段可以显示闭馆或不开放信息，入馆时段分为9:00——10:00,10:00——11:00,11：00——12：00,12:00——13:00,13:00——14:00,14:00——15:30，每个时段显示预约已满或未满，不可预约的日期与可预约的日期颜色上做出区分。

![](https://cdn.nlark.com/yuque/0/2025/png/38454556/1753431179550-7993cf9f-fdb5-4cec-8b1e-beb74dc8a0c2.png)

（中国航天博物馆）  
个人预约量：每周四五600人（每个时间段100人）；每周六800人，9点和13点每个时段为100人，10点、11点、12点、14点每个时段为150人。  
个人预约未按照参观时间段参观，此参观时间段的号源自动释放为全天号，但该参观者的号依旧保留。  
一个时段，只剩一个名额时，预约时如果填选儿童，也可携带2名未成年人。  
5.证件类型：身份证、护照、港澳通行证、外国人永久居留证。  
6.界面可以分为两栏，一栏为预约栏，可进行预约和二维码展示；另一栏为个人栏，可提前登记个人信息，添加参观人员信息，待放票后，点击“预约”按钮进行预约。未满14周岁(含)的儿童不能单独预约，须与成年人一同预约，并与预约人同步核验入馆。  
预约成功后，可显示动态二维码，二维码60秒内有效，一人一码。  
个人预约查询中，显示下拉菜单包括全部、待参观、已参观、已取消。个人预约查询后可显示姓名、身份证号、参观日期、参观时间段、参观人数。  
  ![](https://cdn.nlark.com/yuque/0/2025/png/38454556/1753431199863-0423211a-3cfa-459a-8772-3aef71d30da8.png)  
![](https://cdn.nlark.com/yuque/0/2025/png/38454556/1753431213293-1ff3d211-61c1-4fc2-b595-2535d3baf2a1.png)  
（哈工大博物馆）  
7.一键取消功能。取消预约，下拉选项选择需要退票的人员叉掉即可，或者点击取消键直接取消。  
8.未到馆参观者须在参观当日预约时间前在官方预约平台中取消预约，累计2次逾期未取消预约的参观者，将被列入预约失信人员名单，自第二次未到馆的次日起60天内不能预约本馆。取消预约的号源自动分配到下一个时段的号源中。  
9.携带儿童数量与开门次数匹配。  
10.后台拉黑系统，累计2次逾期未取消预约的参观者，自此人第二次未到馆之日算起，系统将被自动拉黑。管理员可以随时设置拉黑和解除，查询途径菜单包括姓名、身份证号码、拉黑日期、解除日期。  
11.后台预留添加预约信息的端口，后台添加信息后刷身份证，同时在预约人手机显示二维码。  
12.预约系统的开关，预约日期的调整，预约人数的调整，设置管理员操作端口。  
13.年龄60周岁（含）以上的老年人无需预约，刷本人身份证预约系统直接识别，闸机直接开门。

	团体预约  
1.社会团体预约可以选择团体类型、填写团体名称、参观日期时间、人数、参观人员构成、带队人信息联系方式等。团体预约时间段已经预约后，在界面显示已预约。

![](https://cdn.nlark.com/yuque/0/2025/png/38454556/1753431226434-fffd0955-3854-4b9c-8fc0-df858ab2f49e.png)

（西南联大博物馆）  
2.团队成员信息添加方式可以为上传excel表格和手动两种，方便统计和临时添加，添加后自动生成人员信息。人数不符合要求进行提示。信息填写设置截止时间，过时无法再填写。图片提交单位介绍信，有确认键可通知到预约人。

![](https://cdn.nlark.com/yuque/0/2025/png/38454556/1753431237443-1f83ce00-8695-4d6c-893c-38d4cfd10d22.png)![](https://cdn.nlark.com/yuque/0/2025/png/38454556/1753431242860-2eaaf06e-3880-42ec-a905-61bb7526dafb.png)



（中国航天博物馆）  
预约系统后台  
    1.预约系统后台应该要涵盖个人与团体的数据显示，实现精细的查询功能。  
    数据查询要可以按不同条件查询，如按年份、月份、日期、姓名、身份证号进行筛选，预约查询可以显示预约人数、预约时间、身份信息。  
预约系统后台界面应分类显示，开馆当天预约人信息、预约后未到馆人员信息。可显示预约和取消预约的时间。  
     2.数据进行可视化（如柱状图、折线图、饼状图等），可以显示个人预约者比例（老人、成年人、未成年人比例；预约未到馆与多次预约比例）、变化趋势、增长率、同比、环比等。团体预约比例（不同类型团体、实践课、党课、公务接待等）。  
3.预约系统的数据统计功能的精准性，身份证预约信息、校园卡数据是否可统计在一起，每天计算一个总人数。  
4.原有预约系统的数据备份储存。  
5.进入预约系统提示项或者菜单文字管理员可修改，应可写大篇幅。  
校内预约通道（是否具备开通条件，讨论）：  
1.开辟校友预约通道，校友个人预约可以在校友预约通道上认证校友信息（如学籍信息等或与校友办对接），并填写随行人员信息。  
2.校内实践课及党课等活动可以由带队教师在系统上进行预约，选择预约日期时间，填写课程或活动名称、领队人相关信息、是否需要讲解、是否参观校史馆等，填写后在系统上显示出来（类似课表）。