# 中国（哈尔滨）森林博物馆预约系统详细开发文档

## 📋 项目概述

### 项目背景
中国（哈尔滨）森林博物馆需要建立一套现代化的预约管理系统，以规范参观秩序、提升服务质量、优化参观体验。本项目将基于现有的Vue 3 + Element Plus技术栈，将停车场管理系统改造为专业的博物馆预约系统。

### 项目目标
- **业务转型**：从车辆管理转向参观预约管理
- **用户体验提升**：提供便民的前台预约界面和完善的个人中心功能
- **管理效率优化**：建立智能化的预约管理和黑名单管理机制
- **数据分析能力**：提供全面的预约数据统计和可视化分析

### 技术栈
- **前端**：Vue 3.2.14 + Element Plus 1.1.0 + Vue Router 4 + Vuex 4
- **构建工具**：Vue CLI 4.5.0
- **样式**：Sass + Element Plus组件库
- **工具库**：Axios、ECharts、QRCode.js、xlsx

## 🏗️ 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端展示层     │    │   业务逻辑层     │    │   数据存储层     │
│                │    │                │    │                │
│ Vue 3 + Element │◄──►│  RESTful API   │◄──►│   MySQL 数据库   │
│ Plus 组件库     │    │   Spring Boot  │    │   Redis 缓存    │
│                │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 模块划分
1. **用户前台模块**
   - 首页展示
   - 个人预约
   - 团体预约
   - 个人中心

2. **管理后台模块**
   - 预约管理
   - 用户管理
   - 数据统计
   - 系统设置

3. **公共模块**
   - 身份验证
   - 权限控制
   - 二维码生成
   - 消息通知

## 📱 功能模块详细设计

### 1. 首页展示模块

#### 功能描述
博物馆主页面，展示基本信息和功能入口

#### 页面组件
- **MuseumHome.vue** - 首页主组件
- **MuseumInfo.vue** - 博物馆信息展示
- **NavigationMenu.vue** - 导航菜单
- **VisitGuide.vue** - 参观须知

#### 核心功能
- 博物馆背景图片展示（森林主题）
- 开闭馆时间信息（周二至周六9:00-15:30）
- 基本信息展示（地址、电话、交通指引）
- 功能入口导航（个人预约、团体预约、个人中心）
- 参观须知信息（入馆规定、注意事项、安全须知）

#### 技术实现要点
```javascript
// 开馆状态判断
const isOpenToday = computed(() => {
  const today = new Date().getDay();
  // 周二(2)到周六(6)开放，周日(0)周一(1)闭馆
  return today >= 2 && today <= 6;
});

// 当前时间是否在开馆时间内
const isOpenNow = computed(() => {
  const now = new Date();
  const hour = now.getHours();
  const minute = now.getMinutes();
  const currentTime = hour * 60 + minute;
  return currentTime >= 540 && currentTime <= 930; // 9:00-15:30
});
```

### 2. 个人预约模块

#### 功能描述
提供个人用户的预约功能，支持实名认证、同行人管理、时段选择等

#### 页面组件
- **PersonalReservation.vue** - 个人预约主页面
- **ReservationCalendar.vue** - 预约日历组件
- **VisitorForm.vue** - 访客信息表单
- **TimeSlotSelector.vue** - 时段选择器
- **CompanionManager.vue** - 同行人管理
- **QRCodeDisplay.vue** - 二维码显示

#### 核心功能
- **实名认证**：支持身份证、护照、港澳通行证、外国人永久居留证
- **身份验证**：自动识别身份证位数，验证格式，计算年龄
- **年龄限制**：14周岁以下不可单独预约，60周岁以上免预约
- **同行人管理**：一人最多预约5人，需填写完整身份信息
- **时段选择**：6个时段可选，显示剩余名额
- **二维码生成**：预约成功后生成60秒有效的动态二维码

#### 预约规则
- 每周二9:00放本周四五六的号
- 周四五：每时段100人，总计600人
- 周六：9点13点100人/时段，其他150人/时段，总计800人
- 累计2次逾期未取消将被拉黑60天

#### 技术实现要点
```javascript
// 身份证验证
const validateIdCard = (idCard) => {
  const reg18 = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  const reg15 = /^[1-9]\d{5}\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}$/;
  return reg18.test(idCard) || reg15.test(idCard);
};

// 年龄计算
const calculateAge = (idCard) => {
  const birthYear = idCard.length === 18 ? 
    parseInt(idCard.substr(6, 4)) : 
    parseInt('19' + idCard.substr(6, 2));
  return new Date().getFullYear() - birthYear;
};

// 时段容量检查
const checkTimeSlotCapacity = async (date, timeSlot) => {
  const response = await api.getTimeSlotCapacity(date, timeSlot);
  return response.data.remaining > 0;
};
```

### 3. 团体预约模块

#### 功能描述
提供团体用户的预约功能，支持团体信息管理、成员批量导入、审核流程等

#### 页面组件
- **GroupReservation.vue** - 团体预约主页面
- **GroupInfoForm.vue** - 团体信息表单
- **MemberUpload.vue** - 成员信息上传
- **GroupAudit.vue** - 团体预约审核

#### 核心功能
- **团体类型选择**：学校团体、企业团体、社会组织等
- **团体信息填写**：团体名称、联系人信息、预约人数
- **成员信息管理**：支持Excel批量上传和手动添加
- **审核流程**：提交后需要管理员审核确认
- **介绍信上传**：支持单位介绍信图片上传

#### 技术实现要点
```javascript
// Excel文件解析
const parseExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      resolve(jsonData);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
};

// 团体预约提交
const submitGroupReservation = async (formData) => {
  const response = await api.submitGroupReservation({
    ...formData,
    status: 'pending_audit',
    submitTime: new Date().toISOString()
  });
  return response.data;
};
```

### 4. 个人中心模块

#### 功能描述
提供用户个人信息管理、预约查询、预约取消等功能

#### 页面组件
- **PersonalCenter.vue** - 个人中心主页面
- **ReservationHistory.vue** - 预约历史查询
- **ProfileManagement.vue** - 个人信息管理
- **CompanionList.vue** - 常用同行人管理

#### 核心功能
- **预约查询**：支持按状态筛选（全部、待参观、已参观、已取消）
- **预约取消**：支持一键取消和批量取消
- **个人信息管理**：修改个人基本信息
- **同行人管理**：添加、编辑、删除常用同行人信息

### 5. 管理后台模块

#### 功能描述
提供管理员使用的后台管理功能

#### 页面组件
- **AdminDashboard.vue** - 管理后台首页
- **ReservationManagement.vue** - 预约管理
- **BlacklistManagement.vue** - 黑名单管理
- **DataStatistics.vue** - 数据统计
- **SystemSettings.vue** - 系统设置

#### 核心功能
- **预约管理**：查看、审核、修改预约信息
- **黑名单管理**：管理失信人员名单，支持自动拉黑和手动解除
- **数据统计**：提供可视化的预约数据分析
- **系统设置**：配置预约规则、时段设置、通知模板等

## 🗄️ 数据库设计

### 核心数据表

#### 用户表 (users)
```sql
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  real_name VARCHAR(50) NOT NULL,
  id_card VARCHAR(18) UNIQUE NOT NULL,
  phone VARCHAR(11) NOT NULL,
  email VARCHAR(100),
  gender TINYINT,
  birth_date DATE,
  id_card_type TINYINT DEFAULT 1,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 预约记录表 (reservations)
```sql
CREATE TABLE reservations (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  reservation_no VARCHAR(32) UNIQUE NOT NULL,
  reservation_date DATE NOT NULL,
  time_slot VARCHAR(20) NOT NULL,
  total_people INT DEFAULT 1,
  status TINYINT DEFAULT 1,
  type TINYINT DEFAULT 1,
  qr_code VARCHAR(255),
  qr_code_expires_at TIMESTAMP,
  check_in_time TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 同行人员表 (companions)
```sql
CREATE TABLE companions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  reservation_id BIGINT NOT NULL,
  name VARCHAR(50) NOT NULL,
  id_card VARCHAR(18) NOT NULL,
  phone VARCHAR(11),
  gender TINYINT,
  birth_date DATE,
  id_card_type TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (reservation_id) REFERENCES reservations(id)
);
```

#### 黑名单表 (blacklist)
```sql
CREATE TABLE blacklist (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  id_card VARCHAR(18) NOT NULL,
  reason VARCHAR(255),
  blacklist_date DATE NOT NULL,
  release_date DATE,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 🔧 技术实现要点

### 1. 身份证验证系统
- 支持18位和15位身份证格式验证
- 自动计算年龄和性别
- 实现年龄限制规则检查

### 2. 预约时段管理
- 动态时段配置
- 剩余名额实时计算
- 时段冲突检测
- 自动释放机制

### 3. 二维码系统
- 动态二维码生成
- 60秒有效期控制
- 一人一码机制
- 二维码验证接口

### 4. 黑名单自动化
- 逾期未取消自动检测
- 2次违规自动拉黑
- 60天限制期管理
- 黑名单状态同步

## 📅 开发计划

### 第一阶段（1周）：基础架构搭建
- 清理无关功能模块
- 搭建博物馆业务架构
- 改造用户认证系统
- 开发基础组件

### 第二阶段（2周）：核心功能开发
- 个人预约功能
- 团体预约功能
- 预约管理后台
- 身份证验证系统

### 第三阶段（1周）：高级功能开发
- 数据统计分析
- 黑名单管理
- 二维码系统
- 消息通知

### 第四阶段（1周）：测试与优化
- 功能测试
- 性能优化
- 用户体验优化
- 部署上线

## 🎯 项目交付物

1. **前端应用**：完整的Vue 3应用程序
2. **技术文档**：系统架构、API文档、部署文档
3. **用户手册**：用户操作指南、管理员手册
4. **测试报告**：功能测试、性能测试报告
5. **部署包**：生产环境部署包和配置文件

## 📊 质量保证

### 代码质量
- ESLint代码规范检查
- 单元测试覆盖率>80%
- 代码审查机制

### 性能要求
- 页面加载时间<3秒
- 接口响应时间<1秒
- 支持1000并发用户

### 安全要求
- 用户身份验证
- 数据传输加密
- SQL注入防护
- XSS攻击防护
